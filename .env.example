# Web服务配置
WEB_PORT=8080
API_HOST=0.0.0.0

# 数据库配置
DB_PATH=/app/data/xianyu_data.db

# 日志配置
LOG_LEVEL=INFO
DEBUG=false

# 管理员账号配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123

# JWT配置
JWT_SECRET_KEY=your-secret-key-here
SESSION_TIMEOUT=3600

# 多用户系统配置
MULTIUSER_ENABLED=true
USER_REGISTRATION_ENABLED=true
EMAIL_VERIFICATION_ENABLED=true
CAPTCHA_ENABLED=true

# 自动回复配置
AUTO_REPLY_ENABLED=true
AUTO_DELIVERY_ENABLED=true

# AI回复配置
AI_REPLY_ENABLED=false
DEFAULT_AI_MODEL=qwen-plus
DEFAULT_AI_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

# WebSocket配置
WEBSOCKET_URL=wss://wss-goofish.dingtalk.com/
HEARTBEAT_INTERVAL=15
HEARTBEAT_TIMEOUT=5

# 系统资源限制
MEMORY_LIMIT=512
CPU_LIMIT=0.5
