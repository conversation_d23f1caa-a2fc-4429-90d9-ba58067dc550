@import url('variables.css');
@import url('layout.css');
@import url('dashboard.css');
@import url('logs.css');
@import url('keywords.css');
@import url('accounts.css');
@import url('items.css');
@import url('notifications.css');
@import url('components.css');

  .input-field {
    position: relative;
  }

  .input-field label {
    position: absolute;
    top: -0.5rem;
    left: 0.75rem;
    background: #f8fafc;
    padding: 0 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    color: #6b7280;
    z-index: 1;
  }

  .input-field input, .input-field select {
    width: 100%;
    padding: 1rem 0.75rem 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    background: white;
  }

  .input-field input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }

  .add-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 1rem 1.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .add-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
  }

  .add-btn:active {
    transform: translateY(0);
  }

  /* ================================
     【自动回复菜单】相关样式
     ================================ */
  .keywords-list {
    padding: 1.5rem 2rem 2rem;
    max-height: 500px;
    overflow-y: auto;
  }

  .keyword-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    overflow: hidden;
  }

  .keyword-item:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
    transform: translateY(-1px);
  }

  .keyword-item-header {
    padding: 1rem 1.5rem;
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .keyword-tag {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .keyword-actions {
    display: flex;
    gap: 0.5rem;
  }

  .action-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .edit-btn {
    background: #f3f4f6;
    color: #6b7280;
  }

  .edit-btn:hover {
    background: #e5e7eb;
    color: #374151;
  }

  .edit-btn-disabled {
    background: #f9fafb !important;
    color: #9ca3af !important;
    cursor: not-allowed !important;
  }

  .edit-btn-disabled:hover {
    background: #f9fafb !important;
    color: #9ca3af !important;
  }

  .delete-btn {
    background: #fef2f2;
    color: #ef4444;
  }

  .delete-btn:hover {
    background: #fee2e2;
    color: #dc2626;
  }

  .keyword-content {
    padding: 1.5rem;
  }

  .reply-text {
    color: #374151;
    line-height: 1.6;
    margin: 0;
    font-size: 0.875rem;
  }

  .empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6b7280;
  }

  .empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  .empty-state h3 {
    margin: 0 0 0.5rem;
    color: #374151;
  }

  .empty-state p {
    margin: 0 0 2rem;
    font-size: 0.875rem;
  }

  .quick-add-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .quick-add-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  }

  /* 账号选择器现代化 */
  .account-selector {
    background: white;
    border-radius: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    margin-bottom: 2rem;
  }

  .selector-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .selector-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
  }

  .selector-title {
    margin: 0;
    color: #1f2937;
    font-weight: 600;
  }

  .selector-subtitle {
    margin: 0;
    color: #6b7280;
    font-size: 0.875rem;
  }

  .account-select-wrapper {
    position: relative;
  }

  .account-select {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .account-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }

  /* ================================
     【账号管理菜单】相关样式
     ================================ */
  .status-toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
  }

  .status-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .status-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
  }

  .status-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
  }

  input:checked + .status-slider {
    background-color: #10b981;
  }

  input:checked + .status-slider:before {
    transform: translateX(26px);
  }

  .status-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 500;
    min-width: 2rem;
    height: 1.5rem;
  }

  .status-badge.enabled {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
  }

  .status-badge.disabled {
    background: #fef2f2;
    color: #991b1b;
    border: 1px solid #fecaca;
  }

  .account-row.disabled {
    opacity: 0.6;
    background-color: #f9fafb;
  }

  .account-row.disabled .cookie-value {
    background-color: #f3f4f6;
  }

  /* 关键词管理界面的状态提示 */
  .account-badge .badge.bg-warning {
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
  }

  .disabled-account-notice {
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 8px;
    padding: 0.75rem;
    margin-bottom: 1rem;
    color: #92400e;
  }

  .disabled-account-notice .bi {
    color: #f59e0b;
  }



  /* 响应式设计 */
  @media (max-width: 768px) {
    .keyword-input-group {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .keyword-item-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }

    .keyword-actions {
      align-self: flex-end;
    }

    .status-toggle {
      width: 40px;
      height: 20px;
    }

    .status-slider:before {
      height: 14px;
      width: 14px;
      left: 3px;
      bottom: 3px;
    }

    input:checked + .status-slider:before {
      transform: translateX(20px);
    }
  }
  /* ================================
     通用卡片样式 - 适用于所有菜单的卡片
     ================================ */
  .card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    margin-bottom: 2rem;
  }

  .card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    border: none;
    border-radius: 16px 16px 0 0 !important;
    font-weight: 600;
    font-size: 1.1rem;
    padding: 1.25rem 1.5rem;
  }

  .card-body {
    padding: 1.5rem;
  }
  .btn {
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
  }

  .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    border: none;
  }

  .btn-success {
    background: linear-gradient(135deg, var(--success-color), #059669);
    border: none;
  }

  .btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #dc2626);
    border: none;
  }

  .btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
  }

  .btn-outline-primary:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
  }

  .btn-outline-success {
    border: 2px solid var(--success-color);
    color: var(--success-color);
  }

  .btn-outline-success:hover {
    background: var(--success-color);
    border-color: var(--success-color);
    color: white;
  }

  .btn-outline-info {
    border: 2px solid #0dcaf0;
    color: #0dcaf0;
  }

  .btn-outline-info:hover {
    background: #0dcaf0;
    border-color: #0dcaf0;
    color: white;
  }

  .btn-outline-danger {
    border: 2px solid var(--danger-color);
    color: var(--danger-color);
  }

  .btn-outline-danger:hover {
    background: var(--danger-color);
    border-color: var(--danger-color);
    color: white;
  }

  /* ================================
     通用表格样式 - 适用于所有菜单的表格
     ================================ */
  .table {
    margin-bottom: 0;
    background: transparent;
  }

  .table th {
    border-top: none;
    border-bottom: 2px solid var(--border-color);
    font-weight: 600;
    color: var(--dark-color);
    background: rgba(79, 70, 229, 0.05);
    padding: 1rem;
  }

  .table td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
  }

  .table tbody tr:hover {
    background: rgba(79, 70, 229, 0.02);
  }
  .badge {
    font-weight: 500;
    padding: 5px 10px;
  }
  .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  }
  .cookie-value {
    font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
    font-size: 0.85rem;
    background: #f8fafc;
    padding: 0.75rem;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    word-break: break-all;
    line-height: 1.4;
    max-height: 120px;
    overflow-y: auto;
    white-space: pre-wrap;
  }
  .loading {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255,255,255,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
  }
  .loading-spinner {
    width: 3rem;
    height: 3rem;
  }
  .keyword-editor {
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
  }
  .modal-content {
    border-radius: 10px;
    border: none;
  }
  .modal-header {
    border-bottom: 1px solid rgba(0,0,0,0.05);
  }
  .modal-footer {
    border-top: 1px solid rgba(0,0,0,0.05);
  }
  .cookie-id {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 1rem;
  }

  .empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--secondary-color);
  }

  .empty-state i {
    opacity: 0.5;
  }

  .form-control, .form-select {
    border-radius: 10px;
    border: 2px solid var(--border-color);
    transition: all 0.3s ease;
    padding: 0.75rem 1rem;
  }

  .form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.1);
    transform: translateY(-1px);
  }

  .form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
  }

  /* ================================
     通用模态框样式 - 适用于所有菜单的模态框
     ================================ */
  .modal-content {
    border: none;
    border-radius: 16px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
  }

  .modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    border: none;
    border-radius: 16px 16px 0 0;
  }

  .toast-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
  }

  .toast {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
  }

  .btn-group .btn {
    margin-right: 0.25rem;
  }

  .btn-group .btn:last-child {
    margin-right: 0;
  }

  @media (max-width: 768px) {
    .container {
      margin-top: 1rem;
      padding: 0 1rem;
    }

    .card-body {
      padding: 1rem;
    }

    .btn {
      font-size: 0.875rem;
      padding: 0.375rem 0.75rem;
    }

    .cookie-value {
      font-size: 0.75rem;
      max-height: 80px;
    }

    .btn-group {
      flex-direction: column;
    }

    .btn-group .btn {
      margin-right: 0;
      margin-bottom: 0.25rem;
    }

    /* 移动端商品表格优化 */
    #itemsTableBody .btn-group {
      flex-direction: row;
    }

    #itemsTableBody .btn-group .btn {
      padding: 0.2rem 0.4rem;
      font-size: 0.75rem;
    }
  }

  /* ================================
     【商品管理菜单】表格样式
     ================================ */
  #itemsTableBody .btn-group {
    white-space: nowrap;
  }

  #itemsTableBody .btn-group .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;
  }

  #itemsTableBody .btn-group .btn:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  #itemsTableBody .btn-group .btn:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: 0;
  }

  #itemsTableBody .btn-group .btn i {
    font-size: 0.875rem;
  }

  /* 表格操作列样式 */
  .table td:last-child {
    text-align: center;
    vertical-align: middle;
    padding: 0.5rem 0.25rem;
  }

  /* 表格文本截断优化 */
  .table td {
    vertical-align: middle;
  }

  .table td[title] {
    cursor: help;
  }

  /* 商品表格特定优化 */
  #itemsTableBody td:nth-child(4),
  #itemsTableBody td:nth-child(5) {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* 扫码登录按钮特殊样式 */
  .qr-login-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .qr-login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
    background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
  }

  .qr-login-btn:active {
    transform: translateY(0);
  }

  .qr-login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }

  .qr-login-btn:hover::before {
    left: 100%;
  }

  /* 二维码容器样式 */
  .qr-code-wrapper {
    border: 3px solid #28a745;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
    transition: all 0.3s ease;
  }

  .qr-code-wrapper:hover {
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
  }

  /* 步骤指引样式 */
  .step-item {
    text-align: center;
  }

  .step-number {
    font-weight: bold;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
  }

  /* 手动输入按钮样式 */
  .manual-input-btn {
    transition: all 0.3s ease;
  }

  .manual-input-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.2);
  }

  /* 等待提示样式 */
  .bg-light-warning {
    background-color: #fff3cd !important;
  }

  .qr-loading-tip {
    animation: pulse-warning 2s infinite;
  }

  @keyframes pulse-warning {
    0% {
      opacity: 0.8;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.8;
    }
  }

  /* ================================
     【通知渠道菜单】卡片样式
     ================================ */
  .channel-type-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    min-height: 180px; /* 设置最小高度，让卡片更紧凑 */
  }

  .channel-type-card .card-body {
    padding: 1rem; /* 减少内边距 */
  }

  .channel-type-card .channel-icon {
    transition: transform 0.3s ease;
    margin-bottom: 0.75rem !important; /* 减少图标下方间距 */
  }

  .channel-type-card .channel-icon i {
    font-size: 2.2rem !important; /* 减小图标大小 */
  }

  .channel-type-card .card-title {
    font-size: 1.1rem; /* 减小标题字体 */
    margin-bottom: 0.5rem;
  }

  .channel-type-card .card-text {
    font-size: 0.85rem; /* 减小描述文字 */
    margin-bottom: 0.75rem;
  }

  .channel-type-card .btn {
    transition: all 0.3s ease;
    font-size: 0.85rem; /* 减小按钮文字 */
    padding: 0.375rem 0.75rem; /* 减小按钮内边距 */
  }

  .channel-type-card:hover {
    transform: translateY(-3px); /* 减少悬停位移 */
    box-shadow: 0 6px 20px rgba(0,0,0,0.12);
    border-color: var(--bs-primary);
  }

  .channel-type-card:hover .channel-icon {
    transform: scale(1.05); /* 减少悬停缩放 */
  }

  .channel-type-card:hover .btn {
    transform: scale(1.02); /* 减少按钮悬停缩放 */
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .qr-login-btn, .manual-input-btn {
      margin-bottom: 10px;
      font-size: 0.9rem;
    }

    .step-item {
      margin-bottom: 15px;
    }

    .step-number {
      width: 25px !important;
      height: 25px !important;
      font-size: 12px;
    }

    .channel-type-card {
      margin-bottom: 10px;
      min-height: 160px; /* 移动端稍微减小高度 */
    }

    .channel-type-card .card-body {
      padding: 0.75rem; /* 移动端减少内边距 */
    }

    .channel-type-card .channel-icon i {
      font-size: 2rem !important; /* 移动端图标更小 */
    }

    .channel-type-card .card-title {
      font-size: 1rem; /* 移动端标题更小 */
    }

    .channel-type-card .card-text {
      font-size: 0.8rem; /* 移动端描述文字更小 */
    }
  }

  /* 大屏幕优化 */
  @media (min-width: 1400px) {
    .channel-type-card {
      min-height: 200px; /* 大屏幕稍微增加高度 */
    }
  }

  /* ================================
     【自动回复菜单】图片关键词样式
     ================================ */
  .btn-image {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    border: none !important;
    margin-left: 10px;
  }

  .btn-image:hover {
    background: linear-gradient(135deg, #218838, #1ea085) !important;
    transform: translateY(-1px);
  }

  .keyword-input-group {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
  }

  .keyword-input-group .add-btn {
    white-space: nowrap;
    min-width: auto;
  }

  .image-preview {
    margin-top: 10px;
  }

  .preview-container {
    padding: 10px;
    border: 2px dashed #ddd;
    border-radius: 8px;
    text-align: center;
    background-color: #f8f9fa;
  }

  .keyword-type-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-left: 8px;
  }

  .keyword-type-text {
    background-color: #e3f2fd;
    color: #1976d2;
  }

  .keyword-type-image {
    background-color: #f3e5f5;
    color: #7b1fa2;
  }

  /* 关键词列表中的图片预览 */
  .keyword-image-preview {
    max-width: 60px;
    max-height: 60px;
    border-radius: 4px;
    border: 1px solid #ddd;
    cursor: pointer;
  }

  .keyword-image-preview:hover {
    border-color: #007bff;
    transform: scale(1.05);
    transition: all 0.2s ease;
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .keyword-input-group {
      flex-direction: column;
      align-items: stretch;
    }

    .keyword-input-group .add-btn {
      margin-left: 0;
      margin-top: 10px;
    }
  }
