# ================================
# 闲鱼自动回复系统 - Python依赖包
# ================================

# ==================== 核心Web框架 ====================
fastapi>=0.111.0
uvicorn[standard]>=0.29.0
pydantic>=2.7.0

# ==================== 日志记录 ====================
loguru>=0.7.0

# ==================== 网络通信 ====================
websockets>=10.0,<13.0
aiohttp>=3.9.0
requests>=2.31.0
httpx>=0.25.0

# ==================== 配置文件处理 ====================
PyYAML>=6.0.0
python-dotenv>=1.0.1

# ==================== JavaScript执行引擎 ====================
PyExecJS>=1.5.1

# ==================== 协议缓冲区解析 ====================
blackboxprotobuf>=1.0.1

# ==================== 系统监控 ====================
psutil>=5.9.0

# ==================== 文件上传支持 ====================
python-multipart>=0.0.6

# ==================== AI回复引擎 ====================
openai>=1.65.5

# ==================== 图像处理 ====================
Pillow>=10.0.0
qrcode[pil]>=7.4.2

# ==================== 浏览器自动化 ====================
playwright>=1.40.0

# ==================== 加密和安全 ====================
PyJWT>=2.8.0
passlib[bcrypt]>=1.7.4
cryptography>=41.0.0

# ==================== 时间处理 ====================
python-dateutil>=2.8.2

# ==================== Excel文件处理 ====================
pandas>=2.0.0
openpyxl>=3.1.0

# ==================== 邮件发送 ====================
email-validator>=2.0.0

# ==================== 说明 ====================
# 以下模块是Python内置模块，无需安装：
# sqlite3, json, base64, hashlib, hmac, time, datetime, os, sys, re, urllib
# asyncio, threading, pathlib, uuid, random, secrets, traceback, logging
# collections, itertools, functools, copy, pickle, gzip, zipfile, shutil
# tempfile, io, csv, xml, html, http, socket, ssl, subprocess, signal
# inspect, ast, enum, math, decimal, array, queue, contextlib, warnings